import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Images, 
  MessageSquare, 
  FileText, 
  Download, 
  BookOpen, 
  Star,
  Menu,
  X,
  Zap,
  LogOut,
  Settings
} from 'lucide-react';

const AdminLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
    { name: 'Gallery', href: '/admin/gallery', icon: Images },
    { name: 'Contact Messages', href: '/admin/contacts', icon: MessageSquare },
    { name: 'Case Studies', href: '/admin/case-studies', icon: FileText },
    { name: 'Resources', href: '/admin/resources', icon: Download },
    { name: 'Blog Posts', href: '/admin/blog', icon: BookOpen },
    { name: 'Testimonials', href: '/admin/testimonials', icon: Star },
  ];

  const isActive = (href) => {
    if (href === '/admin') {
      return location.pathname === '/admin';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white brutal-border border-r-4 transform ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        
        {/* Logo */}
        <div className="flex items-center justify-between h-20 px-6 brutal-border border-b-4">
          <Link to="/admin" className="flex items-center group">
            <div className="bg-black text-white px-3 py-2 brutal-border brutal-shadow-small group-hover:translate-x-1 group-hover:translate-y-1 group-hover:shadow-none transition-all duration-150">
              <div className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-yellow-400" fill="currentColor" />
                <span className="brutal-text text-sm">ADMIN</span>
              </div>
            </div>
          </Link>
          
          {/* Mobile close button */}
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden brutal-border p-2 brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-8 px-4">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);
              
              return (
                <li key={item.name}>
                  <Link
                    to={item.href}
                    onClick={() => setSidebarOpen(false)}
                    className={`flex items-center gap-3 px-4 py-3 brutal-border brutal-shadow-small transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                      active 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-white text-black hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-bold">{item.name}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom actions */}
        <div className="absolute bottom-0 left-0 right-0 p-4 space-y-2">
          <Link
            to="/admin/settings"
            className="flex items-center gap-3 px-4 py-3 brutal-border brutal-shadow-small bg-white text-black hover:bg-gray-50 transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
          >
            <Settings className="w-5 h-5" />
            <span className="font-bold">Settings</span>
          </Link>
          
          <Link
            to="/"
            className="flex items-center gap-3 px-4 py-3 brutal-border brutal-shadow-small bg-red-500 text-white hover:bg-red-600 transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
          >
            <LogOut className="w-5 h-5" />
            <span className="font-bold">Exit Admin</span>
          </Link>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <header className="bg-white brutal-border border-b-4 h-20 flex items-center justify-between px-6">
          <div className="flex items-center gap-4">
            {/* Mobile menu button */}
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden brutal-border p-2 brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
            >
              <Menu className="w-5 h-5" />
            </button>
            
            <h1 className="brutal-text text-2xl">
              ON-GENERALSERVICES ADMIN
            </h1>
          </div>

          <div className="flex items-center gap-4">
            <div className="bg-green-400 text-black px-4 py-2 brutal-border brutal-shadow-small">
              <span className="font-bold">ADMIN USER</span>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
